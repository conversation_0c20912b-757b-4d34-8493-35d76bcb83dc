'use client';

import { use<PERSON><PERSON>back, useEffect, useMemo, useState } from 'react';
import { LinearGradient } from '@visx/gradient';
import {
  AreaSeries,
  Axis,
  Grid,
  Tooltip,
  XYChart,
  buildChartTheme,
} from '@visx/xychart';
import { utc } from 'moment';
import { format } from 'numerable';
import type { NeuronDeregistrationQueryParams } from '@repo/types/website-api-types';
import { Separator, Skeleton, Text } from '@repo/ui/components';
import { taoDivider, useWindowSize } from '@repo/ui/lib';
import type { DataPoint } from './registration-cost-chart';
import { TimeIntervalSelector } from '@/components/elements/time-interval-selector';
import { Bittensor } from '@/components/icons/bittensor';
import { useDeregistrationHistory } from '@/lib/hooks';

const accessors = {
  xAccessor: (d: DataPoint) => new Date(d.x),
  yAccessor: (d: DataPoint) => d.y,
};

const TickComponent = ({
  x,
  y,
  formattedValue,
}: {
  x: number;
  y: number;
  formattedValue: string | undefined;
}) => (
  <g>
    <line x1={x} x2={x} y1={y - 12.5} y2={y - 8} stroke='#FFFFFF4D' />
    <text
      x={x}
      y={y + 14}
      fill='#FFFFFF'
      opacity={0.4}
      fontSize={12}
      fontWeight={500}
      fontFamily='var(--font-sans)'
      textAnchor='middle'
    >
      {formattedValue}
    </text>
  </g>
);

export const Deregistration = ({ netuid }: { netuid: number }) => {
  const [deregistrationParams, setDeregistrationParams] =
    useState<NeuronDeregistrationQueryParams>({
      netuid,
      timestamp_start: utc().subtract(7, 'day').startOf('day').unix(),
    });
  const { data: histories, isPending } =
    useDeregistrationHistory(deregistrationParams);

  const latestDeregistration = histories?.[0];

  const emissionData = useMemo(
    () =>
      histories
        ?.map((item) => ({
          x: item.timestamp,
          y: Number(item.emission) / taoDivider,
          oldY: Number(item.emission) / taoDivider,
        }))
        .filter((item) => item.x.length > 0) ?? [],
    [histories]
  );

  const incentiveData = useMemo(
    () =>
      histories
        ?.map((item) => ({
          x: item.timestamp,
          y: Number(item.incentive),
          oldY: Number(item.incentive),
        }))
        .filter((item) => item.x.length > 0) ?? [],
    [histories]
  );

  const [emissionMin, emissionMax] = useMemo(() => {
    const max = Math.max(...emissionData.map((d) => d.y));
    const min = Math.min(...emissionData.map((d) => d.y));

    return [min, max];
  }, [emissionData]);

  const [incentiveMin, incentiveMax] = useMemo(() => {
    const max = Math.max(...incentiveData.map((d) => d.y));
    const min = Math.min(...incentiveData.map((d) => d.y));

    return [min, max];
  }, [incentiveData]);

  const convertToEmissionScale = useCallback(
    (data: number[]) => {
      const max = Math.max(...data);
      const min = Math.min(...data);
      const range = max - min;

      const emissionDomain = emissionMax - emissionMin;

      const conversionFactor = emissionDomain / range;

      const convertedData = data.map((value) => {
        return emissionMin + (value - min) * conversionFactor;
      });

      return convertedData;
    },
    [emissionMin, emissionMax]
  );

  const convertEmissionTickToIncentiveScale = useCallback(
    (tick: number) => {
      const emissionDomain = emissionMax - emissionMin;
      const incentiveDomain = incentiveMax - incentiveMin;

      const emissionToIncentiveConversionFactor =
        emissionDomain / incentiveDomain;

      const convertedTick =
        (tick - emissionMin) / emissionToIncentiveConversionFactor +
        incentiveMin;

      return format(convertedTick, '0.0000');
    },
    [emissionMin, emissionMax, incentiveMin, incentiveMax]
  );

  const convertedIncentiveData = useMemo(() => {
    const data = incentiveData.map((d) => d.y);

    const convertedData = convertToEmissionScale(data);

    return convertedData.map((value, index) => {
      return {
        x: incentiveData[index].x,
        y: value,
        oldY: incentiveData[index].y,
      };
    });
  }, [convertToEmissionScale, incentiveData]);

  const handleIntervalChange = useCallback(
    (value: string) => {
      switch (value) {
        case '24H':
          setDeregistrationParams({
            netuid,
            timestamp_start: utc().subtract(24, 'hours').startOf('hour').unix(),
          });
          break;
        case '7D':
          setDeregistrationParams({
            netuid,
            timestamp_start: utc().subtract(7, 'day').startOf('day').unix(),
          });
          break;
        case '30D':
          setDeregistrationParams({
            netuid,
            timestamp_start: utc().subtract(30, 'day').startOf('day').unix(),
          });
          break;
        case 'ALL':
          setDeregistrationParams({
            netuid,
          });
          break;
      }
    },
    [netuid, setDeregistrationParams]
  );

  const customTheme = buildChartTheme({
    backgroundColor: '',
    colors: ['#EB5347', '#04ad96'],
    gridColor: '#e8e8e8',
    gridColorDark: '#222831',
    tickLength: 1,
  });

  useEffect(() => {
    setDeregistrationParams({
      netuid,
      timestamp_start: utc().subtract(7, 'day').startOf('day').unix(),
    });
  }, [netuid, setDeregistrationParams]);

  const isMobile = useWindowSize().isMobile;

  return (
    <>
      {isPending ? <Skeleton className='h-[450px]' /> : null}
      <div className={isPending ? 'hidden' : 'space-y-2'}>
        <div className='flex flex-wrap items-center justify-between gap-2'>
          <div className='flex flex-col gap-3 md:flex-row md:items-center'>
            <Text
              level='sm'
              className='flex w-fit rounded-full bg-neutral-800 px-3 py-1 md:-mt-4'
            >
              <span className='pr-2 font-thin'>
                Last Deregistration Emission:{' '}
              </span>
              <Bittensor className='-mb-0.5 -ml-2 -mr-1' />
              {format(
                Number(latestDeregistration?.emission ?? '0') / taoDivider,
                '0.0000'
              )}
            </Text>
            <Text
              level='sm'
              className='flex w-fit rounded-full bg-neutral-800 px-3 py-1 md:-mt-4'
            >
              <span className='pr-2 font-thin'>
                Last Deregistration Incentive:{' '}
              </span>
              {format(latestDeregistration?.incentive ?? 0, '0.0000')}
            </Text>
          </div>
          <TimeIntervalSelector
            onIntervalChange={handleIntervalChange}
            defaultSelect='7D'
          />
        </div>
        <XYChart
          height={400}
          xScale={{ type: 'time' }}
          yScale={{
            type: 'linear',
            domain: [emissionMin, emissionMax],
            zero: false,
          }}
          margin={{
            top: 20,
            right: isMobile ? 60 : 80,
            bottom: 40,
            left: isMobile ? 90 : 100,
          }}
          theme={customTheme}
        >
          <Grid
            rows={false}
            lineStyle={{
              stroke: '#FFFFFF',
              strokeOpacity: 0.2,
              strokeWidth: 1,
              strokeDasharray: '4 4',
            }}
          />
          <Grid
            columns={false}
            lineStyle={{
              stroke: '#FFFFFF',
              strokeOpacity: 0.2,
              strokeWidth: 1,
              strokeDasharray: '0',
            }}
          />
          <defs>
            <LinearGradient
              id='gradient'
              from='#00DBBC'
              fromOpacity={0.2}
              to='#00DBBC'
              toOpacity={0}
              x1='100%'
              y1='0%'
              x2='100%'
              y2='100%'
            />
            <LinearGradient
              id='gradient2'
              from='#EB5347'
              fromOpacity={0.2}
              to='#EB5347'
              toOpacity={0}
              x1='100%'
              y1='0%'
              x2='100%'
              y2='100%'
            />
          </defs>
          <Axis
            hideAxisLine
            hideTicks
            orientation='right'
            numTicks={8}
            label='Emission (TAO)'
            tickFormat={(taoValue: number) => format(taoValue, '0.000')}
            labelProps={{
              fill: '#00DBBC',
              opacity: 0.6,
              fontSize: 12,
              fontWeight: 500,
              fontFamily: 'var(--font-sans)',
              dx: '45px',
            }}
            tickLabelProps={() => ({
              fill: '#00DBBC',
              opacity: 0.4,
              fontSize: 12,
              fontWeight: 500,
              fontFamily: 'var(--font-sans)',
              dx: '10px',
            })}
          />
          <Axis
            hideAxisLine
            hideTicks
            orientation='left'
            numTicks={8}
            label='Incentive'
            tickFormat={(tick: number) =>
              convertEmissionTickToIncentiveScale(tick)
            }
            labelProps={{
              fill: '#EB5347',
              fontSize: 12,
              opacity: 0.6,
              fontWeight: 500,
              fontFamily: 'var(--font-sans)',
              dx: '-60px',
            }}
            tickLabelProps={() => ({
              fill: '#EB5347',
              opacity: 0.4,
              fontSize: 12,
              fontWeight: 500,
              fontFamily: 'var(--font-sans)',
              dx: '-15px',
            })}
          />
          <Axis
            hideAxisLine
            hideTicks
            orientation='bottom'
            numTicks={isMobile ? 4 : 10}
            tickFormat={(date: string) => {
              return utc(date).format('DD MMM');
            }}
            tickComponent={TickComponent}
            tickLabelProps={() => ({
              fill: '#FFFFFF',
              opacity: 0.4,
              fontSize: 12,
              fontWeight: 500,
              fontFamily: 'var(--font-sans)',
              dy: '10px',
            })}
          />
          <AreaSeries
            dataKey='Line 2'
            data={convertedIncentiveData.slice().reverse()}
            yAccessor={(d) => d.y}
            xAccessor={accessors.xAccessor}
            fill='url(#gradient2)'
            lineProps={{
              strokeWidth: 2,
            }}
          />
          <AreaSeries
            dataKey='Line 1'
            data={emissionData.slice().reverse()}
            yAccessor={accessors.yAccessor}
            xAccessor={accessors.xAccessor}
            fill='url(#gradient)'
            lineProps={{
              strokeWidth: 2,
            }}
          />
          <Tooltip
            snapTooltipToDatumX
            snapTooltipToDatumY
            showVerticalCrosshair
            showSeriesGlyphs
            verticalCrosshairStyle={{
              stroke: '#FFFFFF99',
              strokeDasharray: '4 4',
            }}
            renderTooltip={({ tooltipData }) => {
              if (!tooltipData || !tooltipData.nearestDatum) {
                return null;
              }

              const { datum } = tooltipData.nearestDatum;
              const emissionDatum = emissionData.find(
                (d) => d.x === (datum as DataPoint).x
              );
              const incentiveDatum = convertedIncentiveData.find(
                (d) => d.x === (datum as DataPoint).x
              );

              if (!emissionDatum || !incentiveDatum) {
                return null;
              }

              return (
                <div className='z-10 flex flex-col gap-2 rounded-lg border border-[#323232] bg-[#1D1D1D]/60 p-3 backdrop-blur-xl'>
                  <Text level='sm' className='font-medium opacity-70'>
                    {utc((datum as DataPoint).x).format('DD MMM YYYY')}
                  </Text>
                  <Separator />
                  <div className='flex flex-col items-start'>
                    <div className='flex flex-col items-start'>
                      <Text level='sm' className='max-w-60 opacity-50'>
                        Emission
                      </Text>
                      <Text
                        className='text-ocean flex items-center'
                        level='base'
                      >
                        <Bittensor className='text-ocean -mb-0.5 -ml-2 -mr-1' />
                        {format(emissionDatum.oldY, '0.0000')}
                      </Text>
                    </div>
                    <div className='flex flex-col items-start'>
                      <Text level='sm' className='max-w-60 opacity-50'>
                        Incentive
                      </Text>
                      <Text
                        className='flex items-center text-[#EB5347]'
                        level='base'
                      >
                        {format(incentiveDatum.oldY, '0.0000')}
                      </Text>
                    </div>
                  </div>
                </div>
              );
            }}
          />
        </XYChart>
      </div>
    </>
  );
};
