'use client';

import { useC<PERSON>back, useEffect, useMemo, useState } from 'react';
import { AxisBottom, AxisLeft } from '@visx/axis';
import { GridColumns, GridRows } from '@visx/grid';
import { Group } from '@visx/group';
import { useScreenSize } from '@visx/responsive';
import { scaleBand, scaleLinear } from '@visx/scale';
import { useTooltip } from '@visx/tooltip';
import { utc } from 'moment';
import { format } from 'numerable';
import type {
  NeuronRegistration,
  NeuronRegistrationQueryParams,
} from '@repo/types/website-api-types';
import { Separator, Skeleton, Text } from '@repo/ui/components';
import { useWindowSize } from '@repo/ui/lib';
import { TimeIntervalSelector } from '@/components/elements/time-interval-selector';
import { useSubnetImmune } from '@/lib/hooks';
import { useSubnetHeaderOpen } from '@/lib/hooks/single-subnet-header';

export type BarGroupProps = {
  width?: number;
  height: number;
  margin?: { top: number; right: number; bottom: number; left: number };
  events?: boolean;
  netuid: number;
};

interface TooltipData {
  timestamp: string;
  count: number;
}

const margin = { top: 30, right: 0, bottom: 60, left: 60 };

export const SubnetImmuneChart = ({ netuid }: { netuid: number }) => {
  const height = 400;

  const { open } = useSubnetHeaderOpen();
  const { width } = useScreenSize({ debounceTime: 150 });
  const controlWidth = width - (open ? 0 : width > 1152 ? 560 : 0);

  const xMax = controlWidth - margin.left - margin.right - 80;
  const yMax = height - margin.top * 2;
  const tooltipWidth = 200;

  const [immuneParams, setImmuneParams] =
    useState<NeuronRegistrationQueryParams>({
      netuid,
      timestamp_start: utc().subtract(7, 'day').startOf('day').unix(),
    });
  const { data: immune, isPending } = useSubnetImmune(immuneParams);

  const { isMobile } = useWindowSize();

  const {
    tooltipOpen,
    tooltipLeft,
    tooltipTop,
    tooltipData,
    hideTooltip,
    showTooltip,
  } = useTooltip<TooltipData>();

  const groupByDate = (items: NeuronRegistration[] | undefined) => {
    const countsPerDate = new Map();

    items?.forEach((item) => {
      const date = item.timestamp.split('T')[0];

      countsPerDate.set(date, (countsPerDate.get(date) || 0) + 1);
    });

    return Array.from(countsPerDate, ([timestamp, count]) => ({
      count,
      timestamp,
    }));
  };

  // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
  const chartData = useMemo(() => {
    const result = groupByDate(immune)
      .reverse()
      .map((item) => {
        return {
          count: item.count,
          timestamp: item.timestamp,
        };
      });

    return result;
  }, [immune]);

  const xScale = useMemo(
    () =>
      scaleBand<string>({
        range: [0, xMax],
        round: true,
        domain: chartData.map((d) => `${d.timestamp}`),
        padding: 0.2,
      }),
    [chartData, xMax]
  );

  const yScale = useMemo(
    () =>
      scaleLinear<number>({
        range: [yMax, 0],
        round: true,
        domain: [0, Math.max(...chartData.map((d) => d.count))],
      }),
    [chartData, yMax]
  );

  let tooltipTimeout: NodeJS.Timeout;

  const hideTooltipWithDelay = () => {
    tooltipTimeout = setTimeout(hideTooltip, 1000);
  };

  const cancelTooltipHide = () => {
    clearTimeout(tooltipTimeout);
  };

  const handleIntervalChange = useCallback(
    (value: string) => {
      switch (value) {
        case '24H':
          setImmuneParams({
            netuid,
            timestamp_start: utc().subtract(24, 'hours').startOf('hour').unix(),
          });
          break;
        case '7D':
          setImmuneParams({
            netuid,
            timestamp_start: utc().subtract(7, 'day').startOf('day').unix(),
          });
          break;
        case '30D':
          setImmuneParams({
            netuid,
            timestamp_start: utc().subtract(30, 'day').startOf('day').unix(),
          });
          break;
        case 'ALL':
          setImmuneParams({
            netuid,
            timestamp_start: utc('2025-02-13').startOf('day').unix(),
          });
          break;
      }
    },
    [netuid, setImmuneParams]
  );

  useEffect(() => {
    setImmuneParams({
      netuid,
      timestamp_start: utc().subtract(7, 'day').startOf('day').unix(),
    });
  }, [netuid, setImmuneParams]);

  return (
    <>
      {isPending ? <Skeleton className='h-[450px]' /> : null}
      <div className={isPending ? 'hidden' : 'relative space-y-2'}>
        <div className='flex w-full justify-end'>
          <TimeIntervalSelector
            onIntervalChange={handleIntervalChange}
            defaultSelect='7D'
          />
        </div>
        {/* biome-ignore lint/a11y/noSvgWithoutTitle: <explanation> */}
        <svg width={width} height={400}>
          <Group top={margin.top} left={margin.left}>
            <GridRows
              scale={yScale}
              width={xMax}
              stroke='#FFFFFF'
              strokeWidth={1}
              strokeOpacity={0.2}
            />
            <GridColumns
              scale={xScale}
              height={yMax}
              stroke='#FFFFFF'
              strokeOpacity={0.2}
              strokeWidth={1}
              strokeDasharray='4 4'
            />
            {chartData.map((d, index) => {
              const barWidth = xScale.bandwidth();
              const barHeight = yMax - (yScale(d.count) ?? 0);
              const barX = xScale(`${d.timestamp}`);
              const barY = yMax - barHeight;
              return (
                <Bar
                  key={`bar-${index}-stake`}
                  x={barX}
                  y={barY}
                  width={barWidth}
                  height={barHeight}
                  fill='#00DBBC'
                  onMouseOver={(event: any) => {
                    let adjustedLeft =
                      event.clientX - (open ? 0 : width > 1152 ? 560 : 0);
                    const adjustedTop = event.clientY - 200;

                    if (
                      event.clientX -
                        (open ? 0 : width > 1152 ? 560 : 0) +
                        tooltipWidth >
                      controlWidth
                    ) {
                      adjustedLeft =
                        event.clientX -
                        tooltipWidth -
                        (open ? 0 : width > 1152 ? 560 : 0);
                    }

                    showTooltip({
                      tooltipLeft: adjustedLeft,
                      tooltipTop: adjustedTop,
                      tooltipData: d,
                    });
                    cancelTooltipHide();
                  }}
                  onFocus={(event: any) => {
                    let adjustedLeft =
                      event.clientX - (open ? 0 : width > 1152 ? 560 : 0);
                    const adjustedTop = event.clientY - 200;

                    if (
                      event.clientX -
                        (open ? 0 : width > 1152 ? 560 : 0) +
                        tooltipWidth >
                      controlWidth
                    ) {
                      adjustedLeft =
                        event.clientX -
                        tooltipWidth -
                        (open ? 0 : width > 1152 ? 560 : 0);
                    }

                    showTooltip({
                      tooltipLeft: adjustedLeft,
                      tooltipTop: adjustedTop,
                      tooltipData: d,
                    });
                    cancelTooltipHide();
                  }}
                  onMouseOut={hideTooltipWithDelay}
                  onBlur={hideTooltipWithDelay}
                />
              );
            })}
            <AxisLeft
              scale={yScale}
              tickFormat={(weight) => format(Number(weight), '0a')}
              numTicks={7}
              hideAxisLine
              tickLength={4}
              tickStroke='#FFFFFF33'
              label='Immune Count'
              labelProps={{
                fill: '#FFFFFF',
                opacity: 0.6,
                fontSize: 12,
                fontWeight: 500,
                fontFamily: 'var(--font-sans)',
                dy: '20px',
              }}
              tickLabelProps={() => ({
                fill: '#FFFFFF',
                opacity: 0.4,
                fontSize: 12,
                fontWeight: 500,
                fontFamily: 'var(--font-sans)',
                dx: '-30px',
              })}
            />
            <AxisBottom
              hideAxisLine
              top={yMax}
              scale={xScale}
              numTicks={isMobile ? 4 : 8}
              tickLength={4}
              tickStroke='#FFFFFF4D'
              tickFormat={(date: string) => utc(date).format('MMM DD')}
              tickLabelProps={() => ({
                fill: '#FFFFFF',
                textAnchor: 'middle',
                fontSize: 12,
                opacity: 0.4,
                fontWeight: 500,
                fontFamily: 'var(--font-sans)',
                dy: '5px',
              })}
            />
          </Group>
        </svg>
        {tooltipOpen && tooltipData ? (
          <div
            className='pointer-events-none absolute z-10 flex flex-col gap-2 rounded-lg border border-[#323232] bg-[#1D1D1D]/60 p-3 backdrop-blur-xl'
            style={{ left: tooltipLeft, top: tooltipTop }}
            onMouseOver={cancelTooltipHide}
            onFocus={cancelTooltipHide}
            onMouseOut={hideTooltipWithDelay}
            onBlur={hideTooltipWithDelay}
          >
            <Text level='sm' className='whitespace-nowrap font-medium'>
              {utc(tooltipData.timestamp).format('MMM D, YYYY')}
            </Text>
            <Separator />
            <div className='flex flex-row gap-1'>
              <Text
                level='sm'
                className='whitespace-nowrap font-medium opacity-70'
              >
                Immune Count:
              </Text>
              <Text level='sm' className='font-medium'>
                {tooltipData.count}
              </Text>
            </div>
          </div>
        ) : null}
      </div>
    </>
  );
};
