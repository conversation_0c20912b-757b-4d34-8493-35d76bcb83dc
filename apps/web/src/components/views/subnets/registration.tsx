import { DeregistrationChartDynamic } from './deregistration-chart.dynamic';
import { HeaderDescriptionCard } from './header-description-card';
import { RegistrationChartDynamic } from './registration-chart.dynamic';
import { SubnetImmuneChartDynamic } from './subnet-immune-chart.dynamic';

export function RegistrationView({
  params: { id },
}: {
  params: { id: string };
}) {
  return (
    <>
      <HeaderDescriptionCard
        title='Subnet'
        secondaryTitle='Registration Data'
        description='A chart declaring the historical cost in tao to register a node on the subnet'
      />
      <RegistrationChartDynamic netuid={Number(id)} />
      <HeaderDescriptionCard
        title='Subnet'
        secondaryTitle='Deregistration Data'
        description='This is a running score of the incentive & emission of the last miners de-registered.'
      />
      <DeregistrationChartDynamic netuid={Number(id)} />
      <HeaderDescriptionCard title='Subnet' secondaryTitle='Immune Miner' />
      <SubnetImmuneChartDynamic netuid={Number(id)} />
      {/* <div className="flex flex-col">
				<RegistrationTable subnetId={id} />
			</div> */}
    </>
  );
}
